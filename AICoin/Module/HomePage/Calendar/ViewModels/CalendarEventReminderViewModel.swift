//
//  CalendarEventReminderViewModel.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-19.
//  Copyright © 2024 AICoin. All rights reserved.
//

import Foundation

/// 日历事件提醒视图模型
class CalendarEventReminderViewModel {

    // MARK: - 类型定义

    /// 提醒类型
    enum ReminderType: Int, CaseIterable {
        case eventReminder = 0  // 事件提醒
        case historyReminder = 1  // 历史提醒

        var title: String {
            switch self {
            case .eventReminder:
                return "事件提醒".base.localized
            case .historyReminder:
                return "历史提醒".base.localized
            }
        }

        var apiType: String {
            switch self {
            case .eventReminder:
                return "current"
            case .historyReminder:
                return "history"
            }
        }
    }

    /// 加载状态
    enum LoadingState {
        case idle
        case loading
        case loaded
        case error(Error)
    }

    // MARK: - 属性

    /// 当前提醒类型
    private(set) var currentReminderType: ReminderType = .eventReminder

    /// 数据源
    private(set) var dataSections: [CalendarEventReminderViewModel.EventReminderSectionModel] = []

    /// 加载状态
    private(set) var loadingState: LoadingState = .idle

    /// 网络请求任务
    private var currentTask: URLSessionDataTask?

    // MARK: - 回调

    /// 数据加载完成回调
    var didLoadData: (() -> Void)?

    /// 加载状态变化回调
    var didChangeLoadingState: ((LoadingState) -> Void)?

    /// 错误处理回调
    var didEncounterError: ((Error) -> Void)?

    // MARK: - 公共方法

    /// 设置提醒类型并加载数据
    func setReminderType(_ type: ReminderType) {
        currentReminderType = type
        loadReminderList()
    }

    /// 加载提醒列表
    func loadReminderList() {
        // 取消之前的请求
        currentTask?.cancel()

        // 更新加载状态
        updateLoadingState(.loading)

        // 构造请求参数
        let params: [String: Any] = [
            "remindType": currentReminderType.apiType
        ]

        // 发起网络请求
        currentTask = AICHttpManager.shared.post(
            "/api/upgrade/calendar/remind/list",
            parameters: params,
            progress: nil,
            success: { [weak self] task, response in
                guard let self = self else { return }
                self.handleAPIResponse(response)
            },
            failure: { [weak self] task, error in
                guard let self = self else { return }
                self.handleAPIError(error)
            }
        )
    }

    /// 刷新数据
    func refreshData() {
        loadReminderList()
    }

    /// 取消网络请求
    func cancelRequest() {
        currentTask?.cancel()
        currentTask = nil
    }

    // MARK: - 数据处理

    /// 处理API响应
    private func handleAPIResponse(_ response: Any?) {
        let json = JSON(response ?? "")

        if json["success"].boolValue {
            // 解析数据
            if let apiData = CalendarEventReminderAPIData.model(withJSON: json["data"].rawValue) {
                processAPIData(apiData)
            } else {
                let error = NSError(
                    domain: "CalendarEventReminderError",
                    code: -1,
                    userInfo: [NSLocalizedDescriptionKey: "数据解析失败"]
                )
                handleAPIError(error)
            }
        } else {
            // API返回错误
            let errorCode = json["errorCode"].intValue
            let errorMessage = json["error"].stringValue
            let error = NSError(
                domain: "CalendarEventReminderError",
                code: errorCode,
                userInfo: [NSLocalizedDescriptionKey: errorMessage.isEmpty ? "请求失败" : errorMessage]
            )
            handleAPIError(error)
        }
    }

    /// 处理API错误
    private func handleAPIError(_ error: Error) {
        updateLoadingState(.error(error))
        didEncounterError?(error)
    }

    /// 处理API数据
    private func processAPIData(_ apiData: CalendarEventReminderAPIData) {
        // 将API数据转换为CalendarEventModel
        let events = apiData.list.map { CalendarEventModel.fromAPIItem($0) }

        // 按日期分组
        dataSections = groupEventsByDate(events)

        // 更新状态
        updateLoadingState(.loaded)
        didLoadData?()
    }

    /// 按日期分组事件
    private func groupEventsByDate(_ events: [CalendarEventModel])
        -> [CalendarEventReminderViewModel.EventReminderSectionModel]
    {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "MM月dd日"

        // 按日期分组
        let groupedEvents = Dictionary(grouping: events) { event in
            dateFormatter.string(from: event.date)
        }

        // 转换为分组模型并排序
        let sections = groupedEvents.map { (dateString, events) in
            return CalendarEventReminderViewModel.EventReminderSectionModel(
                dateString: dateString, events: events)
        }

        // 按日期排序
        return sections.sorted { section1, section2 in
            // 这里可以根据需要实现更复杂的排序逻辑
            return section1.dateString < section2.dateString
        }
    }

    /// 更新加载状态
    private func updateLoadingState(_ state: LoadingState) {
        loadingState = state
        didChangeLoadingState?(state)
    }

    // MARK: - 数据访问

    /// 获取分组数量
    func numberOfSections() -> Int {
        return dataSections.count
    }

    /// 获取指定分组的事件数量
    func numberOfEvents(in section: Int) -> Int {
        guard section < dataSections.count else { return 0 }
        return dataSections[section].events.count
    }

    /// 获取指定位置的事件
    func event(at indexPath: IndexPath) -> CalendarEventModel? {
        guard indexPath.section < dataSections.count,
            indexPath.row < dataSections[indexPath.section].events.count
        else {
            return nil
        }
        return dataSections[indexPath.section].events[indexPath.row]
    }

    /// 更新事件数据
    func updateEvent(_ updatedEvent: CalendarEventModel) {
        // 更新dataSections中的事件数据
        for (sectionIndex, section) in dataSections.enumerated() {
            for (eventIndex, event) in section.events.enumerated() {
                if event.id == updatedEvent.id {
                    dataSections[sectionIndex].events[eventIndex] = updatedEvent

                    // 通知UI更新
                    didLoadData?()
                    return
                }
            }
        }
    }

    /// 获取指定分组的日期字符串
    func dateString(for section: Int) -> String? {
        guard section < dataSections.count else { return nil }
        return dataSections[section].dateString
    }

    /// 判断是否为空数据
    var isEmpty: Bool {
        return dataSections.isEmpty
    }

    /// 判断是否正在加载
    var isLoading: Bool {
        if case .loading = loadingState {
            return true
        }
        return false
    }

    // MARK: - 数据模型

    /// 事件提醒分组模型
    struct EventReminderSectionModel {
        let dateString: String  // 日期字符串，如 "05月18日"
        var events: [CalendarEventModel]  // 该日期下的事件列表
    }
}
