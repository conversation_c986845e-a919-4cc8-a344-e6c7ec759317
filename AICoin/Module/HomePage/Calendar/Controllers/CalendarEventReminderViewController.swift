//
//  CalendarEventReminderViewController.swift
//  AICoin
//
//  Created by <PERSON> on 2024-05-19.
//  Copyright © 2024 AICoin. All rights reserved.
//

import SnapKit
import UIKit

/// 日历事件提醒管理界面
class CalendarEventReminderViewController: AICBaseViewController, WMPageControllerDelegate,
    WMPageControllerDataSource
{

    // MARK: - 类型别名

    /// 提醒类型别名，使用ViewModel中的定义
    typealias ReminderType = CalendarEventReminderViewModel.ReminderType

    // MARK: - 属性

    /// 提醒类型数组
    private lazy var reminderTypes: [ReminderType] = {
        return ReminderType.allCases
    }()

    /// 分页控制器
    private lazy var pageController: HomePageBasePageViewController = {
        let controller = HomePageBasePageViewController()
        controller.delegate = self
        controller.dataSource = self
        controller.selectIndex = 0
        controller.menuViewLayoutMode = .center
        controller.itemMargin = 55
        controller.titleColorSelected = UIColor.baseTheme.current.level2SegmentedSelectedColor
        controller.titleColorNormal = UIColor.baseTheme.current.level2SegmentedNormalColor
        controller.setCustomWithNormalFont(
            UIFont.systemFont(ofSize: 17),
            selectedFont: UIFont.aic_mediumFont(withSize: 17)
        )
        controller.titleSizeNormal = 17
        controller.titleSizeSelected = 17
        controller.progressColor = UIColor.home.current.inNavPageVCIndicatorColor
        controller.menuViewBgColor = .clear
        controller.menuViewStyle = .default
        controller.isShowTitleChangeAnimate = false
        controller.scrollViewBGColor = UIColor.baseTheme.current.bgColor
        controller.isShowSeparator = false
        return controller
    }()

    /// 自定义导航栏
    private lazy var navBar: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.baseTheme.current.navBarColor
        return view
    }()

    /// 返回按钮
    private let backButton = UIButton()

    /// 菜单框架
    private var menuFrame: CGRect {
        return CGRect(x: 0, y: aic_statusBarHeight() - 3, width: view.width, height: 44)
    }

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()

        setupNavigation()
        setupViews()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        let frame = view.bounds
        navBar.frame = CGRect(x: 0, y: 0, width: frame.width, height: aic_statusBarHeight() + 44)
        pageController.view.frame = frame
    }

    // MARK: - 设置

    private func setupNavigation() {
        // 隐藏系统导航栏
        aic_navigationBarHidden = true
    }

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        // 添加自定义导航栏
        view.addSubview(navBar)
        addChild(pageController)
        view.addSubview(pageController.view)
        navBar.snp.makeConstraints { make in
            make.top.leading.trailing.equalTo(0)
            make.height.equalTo(aic_statusBarHeight() + 44)
        }

        // 添加返回按钮
        let button = backButton
        let color = UIColor.baseTheme.current.navItemColor
        button.setImage(UIImage.base.image(name: "Icon_Nav_Back")?.byTintColor(color), for: .normal)
        self.view.addSubview(button)
        button.snp.makeConstraints({ (make) in
            make.leading.equalTo(self.navBar).offset(11)
            make.centerY.equalTo(self.navBar).offset(aic_statusBarHeight() / 2)
        })
        button.aic_hitTestSlop = UIEdgeInsets(top: -10, left: -10, bottom: -10, right: -10)
        button.addTarget(self, action: #selector(touchLeftItem), for: .touchUpInside)

    }

    // MARK: - 事件处理

    @objc private func customTouchLeftItem() {
        navigationController?.popViewController(animated: true)
    }

}

// MARK: - WMPageControllerDelegate, WMPageControllerDataSource

extension CalendarEventReminderViewController {

    func numbersOfChildControllers(in pageController: WMPageController) -> Int {
        return reminderTypes.count
    }

    func pageController(_ pageController: WMPageController, titleAt index: Int) -> String {
        return reminderTypes[index].title
    }

    func pageController(_ pageController: WMPageController, viewControllerAt index: Int)
        -> UIViewController
    {
        let reminderType = reminderTypes[index]
        return CalendarEventReminderListViewController(reminderType: reminderType)
    }

    func pageController(_ pageController: WMPageController, preferredFrameFor menuView: WMMenuView)
        -> CGRect
    {
        return menuFrame
    }

    func pageController(
        _ pageController: WMPageController, preferredFrameForContentView contentView: WMScrollView
    ) -> CGRect {
        let menuFrame = self.menuFrame
        return CGRect(
            x: 0, y: menuFrame.maxY + 3, width: view.width, height: view.height - menuFrame.maxY)
    }

    func pageController(
        _ pageController: WMPageController, didEnter viewController: UIViewController,
        withInfo info: [AnyHashable: Any]
    ) {
        // 页面切换完成后的处理
    }
}

// MARK: - 子视图控制器

/// 事件提醒列表视图控制器
class CalendarEventReminderListViewController: AICBaseViewController {

    // MARK: - 属性

    /// 提醒类型
    private let reminderType: CalendarEventReminderViewController.ReminderType

    /// 视图模型
    private let viewModel = CalendarEventReminderViewModel()

    /// 表格视图
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .grouped)
        tableView.backgroundColor = UIColor.baseTheme.current.bgColor
        tableView.separatorStyle = .none
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(
            CalendarEventReminderCell.self, forCellReuseIdentifier: "CalendarEventReminderCell")
        tableView.register(
            CalendarEventReminderHeaderView.self,
            forHeaderFooterViewReuseIdentifier: "CalendarEventReminderHeaderView")
        // 禁用下拉刷新
        tableView.alwaysBounceVertical = false
        return tableView
    }()

    /// 空视图模型
    private lazy var emptyModel: BaseEmptyDelegateModel = {
        let model = BaseEmptyDelegateModel()
        model.titleString = "暂无记录".base.localized
        model.image = UIImage.ticker.image(name: "暂无内容")
        return model
    }()

    // MARK: - 初始化

    init(reminderType: CalendarEventReminderViewController.ReminderType) {
        self.reminderType = reminderType
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()

        setupViews()
        setupEmptyView()
        bindViewModel()
        loadData()
    }

    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        // 取消网络请求
        viewModel.cancelRequest()
    }

    // MARK: - 设置

    private func setupViews() {
        view.backgroundColor = UIColor.baseTheme.current.bgColor

        view.addSubview(tableView)

        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }

    private func setupEmptyView() {
        tableView.emptyDataSetSource = emptyModel
        tableView.emptyDataSetDelegate = emptyModel
    }

    // MARK: - 视图模型绑定

    private func bindViewModel() {
        // 数据加载完成回调
        viewModel.didLoadData = { [weak self] in
            DispatchQueue.main.async {
                self?.tableView.reloadData()
                self?.updateEmptyViewState()
            }
        }

        // 加载状态变化回调
        viewModel.didChangeLoadingState = { [weak self] state in
            DispatchQueue.main.async {
                self?.handleLoadingStateChange(state)
            }
        }

        // 错误处理回调
        viewModel.didEncounterError = { [weak self] error in
            DispatchQueue.main.async {
                self?.handleError(error)
            }
        }

        // 删除成功回调
        viewModel.didDeleteEventSuccess = { [weak self] deletedEvent in
            DispatchQueue.main.async {
                CalendarDataSource.shared.updateEvent(deletedEvent)
            }
        }
    }

    // MARK: - 数据加载

    private func loadData() {
        viewModel.setReminderType(reminderType)
    }

    // MARK: - 状态处理

    private func handleLoadingStateChange(_ state: CalendarEventReminderViewModel.LoadingState) {
        switch state {
        case .loading:
            showLoadingView()
        case .loaded, .error:
            hideLoadingView()
        case .idle:
            break
        }
        updateEmptyViewState()
    }

    private func handleError(_ error: Error) {
        // 显示错误提示
        print("加载事件提醒失败: \(error.localizedDescription)")
        // 这里可以添加更友好的错误提示UI
    }

    private func updateEmptyViewState() {
        emptyModel.isShowEmptyView = viewModel.isEmpty && !viewModel.isLoading
    }

    // MARK: - 事件处理

    /// 编辑按钮点击处理
    private func editButtonTapped(at indexPath: IndexPath) {
        // 获取事件数据
        guard let event = viewModel.event(at: indexPath) else {
            print("无法获取事件数据")
            return
        }

        // 创建并显示EventReminderViewController弹窗
        let reminderVC = EventReminderViewController(event: event)

        // 设置编辑完成后的回调
        reminderVC.onReminderSettingSuccess = { [weak self] updatedEvent in
            // 更新ViewModel中的数据
            self?.viewModel.updateEvent(updatedEvent)

            // 关闭弹窗
            reminderVC.dismiss(animated: true, completion: nil)
        }

        // 显示弹窗
        present(reminderVC, animated: true, completion: nil)
    }

    /// 删除按钮点击处理
    private func deleteButtonTapped(at indexPath: IndexPath) {
        // 获取事件数据
        guard let event = viewModel.event(at: indexPath) else {
            print("无法获取事件数据")
            return
        }

        // 调用ViewModel的删除方法
        viewModel.deleteEventReminder(event)
    }

}

// MARK: - UITableViewDataSource, UITableViewDelegate

extension CalendarEventReminderListViewController: UITableViewDataSource, UITableViewDelegate {

    func numberOfSections(in tableView: UITableView) -> Int {
        return viewModel.numberOfSections()
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return viewModel.numberOfEvents(in: section)
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell =
            tableView.dequeueReusableCell(
                withIdentifier: "CalendarEventReminderCell", for: indexPath)
            as! CalendarEventReminderCell

        if let event = viewModel.event(at: indexPath) {
            cell.configure(with: event, reminderType: reminderType)
        }

        // 设置indexPath
        cell.indexPath = indexPath

        // 设置按钮回调
        cell.editButtonTapped = { [weak self] in
            self?.editButtonTapped(at: indexPath)
        }

        cell.deleteButtonTapped = { [weak self] in
            self?.deleteButtonTapped(at: indexPath)
        }

        return cell
    }

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView =
            tableView.dequeueReusableHeaderFooterView(
                withIdentifier: "CalendarEventReminderHeaderView")
            as! CalendarEventReminderHeaderView

        if let dateString = viewModel.dateString(for: section) {
            headerView.configure(with: dateString)
        }
        return headerView
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 78 / 2 + 1  // 增加1像素间距
    }

    //底部间距
    func tableView(_ tableView: UITableView, heightForFooterInSection section: Int) -> CGFloat {
        return 4
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return UITableView.automaticDimension
    }

    func tableView(_ tableView: UITableView, estimatedHeightForRowAt indexPath: IndexPath)
        -> CGFloat
    {
        return 80
    }
}

// MARK: - 自定义单元格
class CalendarEventReminderCell: UITableViewCell {

    // MARK: - 属性

    /// 编辑按钮点击回调
    var editButtonTapped: (() -> Void)?

    /// 删除按钮点击回调
    var deleteButtonTapped: (() -> Void)?

    /// 当前indexPath（用于回调时定位事件）
    var indexPath: IndexPath?

    /// 事件标题标签
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 15)
        label.textColor = DynamicHelper.themeColor(day: 0x292D33, night: 0xFFFFFF)
        label.numberOfLines = 0
        return label
    }()

    /// 提醒时间标签
    private let reminderTimeLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 12, weight: .light)
        label.textColor = DynamicHelper.themeColor(day: 0x7A8899, night: 0xC3C7D9)
        return label
    }()

    /// 编辑按钮
    private lazy var editButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.home.image(name: "icon_bell_selected"), for: .normal)
        button.addTarget(self, action: #selector(editButtonAction), for: .touchUpInside)

        return button
    }()

    /// 删除按钮
    private lazy var deleteButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage.home.image(name: "icon_event_delete"), for: .normal)
        button.addTarget(self, action: #selector(deleteButtonAction), for: .touchUpInside)
        return button
    }()

    /// 按钮容器视图
    private let buttonContainerView: UIView = {
        let view = UIView()
        return view
    }()

    /// 容器视图
    private let containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .baseCurrentCellBgColor
        return view
    }()

    // MARK: - 初始化

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 设置UI

    private func setupViews() {
        selectionStyle = .none
        backgroundColor = .clear

        contentView.addSubview(containerView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(reminderTimeLabel)
        containerView.addSubview(buttonContainerView)

        buttonContainerView.addSubview(editButton)
        buttonContainerView.addSubview(deleteButton)

        setupConstraints()
    }

    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.bottom.equalToSuperview().inset(1)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
            make.right.equalTo(buttonContainerView.snp.left).offset(-16)
        }

        reminderTimeLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.equalTo(titleLabel)
            make.right.equalTo(titleLabel)
            make.bottom.equalToSuperview().offset(-12)
        }

        buttonContainerView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.top.equalToSuperview().offset(12)
            make.width.equalTo(56)
            make.height.equalTo(20)
        }

        editButton.snp.makeConstraints { make in
            make.right.top.equalToSuperview()
            make.width.height.equalTo(20)
        }

        deleteButton.snp.makeConstraints { make in
            make.right.equalTo(editButton.snp.left).offset(-16)
            make.top.equalToSuperview()
            make.width.height.equalTo(20)
        }

    }

    // MARK: - 动作

    @objc private func editButtonAction() {
        editButtonTapped?()
    }

    @objc private func deleteButtonAction() {
        deleteButtonTapped?()
    }

    // MARK: - 公共方法

    func configure(
        with model: CalendarEventModel,
        reminderType: CalendarEventReminderViewController.ReminderType
    ) {
        titleLabel.text = model.title

        // 计算提醒时间
        let reminderDate = model.date.addingTimeInterval(-TimeInterval(model.reminderTime))
        let formatter = DateFormatter()
        formatter.dateFormat = "MM月dd日 HH:mm"
        reminderTimeLabel.text = "提醒时间：\(formatter.string(from: reminderDate))"

        // 如果是历史提醒，则隐藏按钮容器
        buttonContainerView.isHidden = (reminderType == .historyReminder)
    }
}

// MARK: - 自定义头视图
class CalendarEventReminderHeaderView: UITableViewHeaderFooterView {

    // MARK: - 属性

    private let titleLabel: UILabel = {
        let label = UILabel()
        label.font = UIFont.systemFont(ofSize: 13)
        label.textColor = DynamicHelper.themeColor(day: 0x7A8899, night: 0xC3C7D9)
        return label
    }()

    // MARK: - 初始化

    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupViews()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 设置UI

    private func setupViews() {
        contentView.backgroundColor = .baseCurrentCellBgColor

        contentView.addSubview(titleLabel)

        titleLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
    }

    // MARK: - 公共方法

    func configure(with title: String) {
        titleLabel.text = title
    }
}
